{"name": "clue", "version": "2.0.0", "description": "百度泛知识直播间分享页-线索直播", "scripts": {"postinstall": "node node_modules/@baidu/xbox-native/scripts/postinstall.js", "build": "npm run build:sdk && cross-env NODE_ENV=production NODE_OPTIONS=\"--openssl-legacy-provider\" webpack --config build/webpack.config.prod.js", "build:dev": "npm run build:sdk && cross-env NODE_ENV=development NODE_OPTIONS=\"--openssl-legacy-provider\" webpack --progress --config build/webpack.config.prod.js", "build:watch": "npm run build:sdk && cross-env NODE_ENV=development NODE_OPTIONS=\"--openssl-legacy-provider\" webpack --progress --config build/webpack.config.prod.js --watch", "dev": "concurrently --raw \"cross-env NODE_ENV=development NODE_OPTIONS=\\\"--openssl-legacy-provider\\\" webpack-dev-server --config build/webpack.config.dev.js\" \"npm run dev:prefetch\" \"npm run dev:sdk\" \"npm run watch:prefetch\"", "analyz": "cross-env NODE_ANALYZE=true npm run build", "build:sdk": "cd src/abtest-sdk && npm run build", "dev:sdk": "cd src/abtest-sdk && npm run dev", "push": "cross-env yarn build && rmb push", "precommit": "lint-staged", "dev:prefetch": "tsup", "watch:prefetch": "nodemon"}, "keywords": ["live", "media"], "author": "<EMAIL>", "engines": {"node": ">= 20.10.0", "npm": ">= 10.2.3"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.17.9", "@babel/eslint-parser": "^7.15.8", "@babel/eslint-plugin": "^7.14.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.16.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-jsx": "^7.16.0", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@babel/runtime": "^7.16.3", "@babel/runtime-corejs3": "^7.12.5", "@baidu/bend-sdk": "^2.0.4", "@baidu/html-webpack-pre-render-plugin": "^2.3.27", "@ecomfe/eslint-config": "^7.3.0", "@ecomfe/stylelint-config": "^1.1.2", "@swc/core": "^1.11.21", "autoprefixer": "^8.6.3", "babel-loader": "^8.2.3", "babel-plugin-import": "^1.13.5", "chalk": "^2.4.2", "clean-webpack-plugin": "^2.0.0", "copy-webpack-plugin": "^6.4.0", "cross-env": "^5.2.0", "css-loader": "^0.28.11", "eslint": "^7.19.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-webpack-plugin": "^2.6.0", "eventemitter3": "^4.0.7", "fecs": "^1.6.4", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.6.1", "happypack": "^5.0.1", "husky": "^2.0.0", "indexof": "^0.0.1", "less": "^3.0.4", "less-loader": "^4.1.0", "lint-staged": "^8.1.7", "mini-css-extract-plugin": "^0.4.1", "mock-webpack-plugin": "^2.0.0", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^4.0.3", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-loader": "^2.1.5", "postcss-plugin-pr2rem": "^0.1.1", "postcss-preset-env": "^5.1.0", "pre-commit": "^1.2.2", "raw-loader": "^1.0.0", "script-ext-html-webpack-plugin": "^2.1.3", "semver": "^5.3.0", "shelljs": "^0.8.4", "simple-git": "^1.126.0", "style-loader": "^0.21.0", "stylelint": "^13.13.1", "stylelint-webpack-plugin": "^2.1.1", "terser-webpack-plugin": "^4.2.3", "thread-loader": "^3.0.4", "tsup": "^8.4.0", "typescript": "^5.8.3", "url-loader": "^1.0.1", "webpack": "^4.32.2", "webpack-bundle-analyzer": "^3.0.4", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.11.3", "webpack-manifest-plugin": "^3.0.0", "webpack-merge": "^4.1.0", "webpackbar": "^7.0.0"}, "dependencies": {"@baidu/afx-video": "^1.0.5", "@baidu/bdmedialive-scheme": "^2.0.0-beta3", "@baidu/boxx": "=1.2.42", "@baidu/eop-utils": "^0.0.3", "@baidu/guid": "^1.0.1", "@baidu/haokan-im": "^1.0.8", "@baidu/haokan-player": "^1.0.5-2", "@baidu/hplayer": "^1.0.13", "@baidu/im-jssdk": "^0.1.35", "@baidu/live-popup": "^1.0.7", "@baidu/m-wechat-sdk": "1.7.2", "@baidu/mcp-sdk": "^1.20.34", "@baidu/media-player": "0.0.3", "@baidu/mvideo-tool": "^1.2.30", "@baidu/mvideo-ui": "^1.2.15", "@baidu/mvideo-vspeed": "^1.1.31-beta3", "@baidu/nano": "^0.2.3", "@baidu/nano-react": "0.1.0", "@baidu/nano-theme": "^0.2.3", "@baidu/tb-napi": "^1.0.12", "@baidu/ubc-report-sdk": "^2.1.10", "@baidu/ug-invoke-app": "2.3.1", "@baidu/ug-matrix": "^1.1.1", "@baidu/weirwood-sdk": "^1.3.2", "@baidu/wuji-uikit": "^1.3.21", "@baidu/xbox": "^1.2.29", "@baidu/xbox-concern": "^2.0.6", "@baidu/xbox-emoticon": "^2.0.0", "@baidu/xbox-native": "0.3.19", "@baidu/xbox-sdk": "^1.0.7", "@super-fe/BdrainfnAjax": "^1.0.6", "ahooks": "^3.0.0-alpha.2", "antd": "^5.6.0", "antd-mobile": "5.40.0", "axios": "^0.21.0", "babel-polyfill": "^6.23.0", "classnames": "^2.2.6", "clipboard": "2.0.5", "concurrently": "^9.1.2", "copy-to-clipboard": "^3.3.1", "core-decorators": "^0.20.0", "core-js": "^3.19.3", "crc-32": "^1.2.2", "crypto-js": "^4.1.1", "dayjs": "^1.10.4", "fetch-jsonp": "^1.1.3", "gsap": "^3.12.5", "hls.js": "^1.5.13", "js-cookie": "^2.2.1", "lodash": "^4.17.15", "lodash-decorators": "^6.0.1", "lottie-web": "^5.12.2", "md5": "^2.3.0", "mix-img": "^1.0.6", "postcss-plugin-pr2rem": "^0.1.1", "qs": "^6.9.4", "react": "^16.8.6", "react-content-loader": "^5.1.4", "react-dev-inspector": "^1.8.0", "react-dom": "^16.8.3", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.4", "react-lottie": "^1.2.3", "react-redux": "^7.2.2", "react-router-dom": "^4.3.1", "react-suspense-boundary": "^3.0.0", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "smoothscroll-polyfill": "^0.4.4", "swiper": "4.5.1", "timeago.js": "^4.0.2", "tua-body-scroll-lock": "^1.2.1", "video.js": "^7.8.4", "whatwg-fetch": "^3.5.0", "xss": "^1.0.8"}, "license": "UNLICENSED", "lint-staged": {"src/**/*.{js,jsx}": ["eslint --fix", "git add"], "src/**/*.{css,less}": ["stylelint --syntax less --fix", "git add"], "src/**/*.{png,jpg}": ["git add"]}}