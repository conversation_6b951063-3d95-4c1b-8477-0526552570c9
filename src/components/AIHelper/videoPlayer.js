/*
 * @file 视频播放器
 * <AUTHOR>
 * @date 2025-07-28 16:54:13
 */

import React, {useEffect, useRef} from 'react';
import HPlayer from '@baidu/hplayer';
import {aiHelperNativeUtils} from './utils';

export default function VideoPlayer({index, section, isH5}) {
    const player = useRef();
    function onClick() {
        if (!isH5) {
            player.current.pause();
            const {videoUrl, videoCover, id} = section;
            aiHelperNativeUtils.openVideoPlayer(videoUrl, videoCover, id);
        }
    }
    useEffect(() => {
        player.current = new HPlayer({
            container: document.getElementById(`h-video-${index}`),
            video: {
                url: 'https://fc-video.cdn.bcebos.com/a870d924cc63dd60102da2725a64243c.mp4',
                pic: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg'
            },
            fullscreen: 'browser',
            showFullScreen: isH5
        });
        isH5 && player.current.play();
    }, [index, isH5]);
    return (
        <div
            onClick={onClick}
            id={`h-video-${index}`}
            style={{width: '100%', height: '100%'}}
        ></div>
    );
}
