/* eslint-disable max-len */
/*
 * @file 直播小助手
 * <AUTHOR>
 * @date 2025-07-25 11:07:22
 */

import React, {useState, useRef, useEffect} from 'react';
import classNames from 'classnames';
import './index.less';
import VideoPlayer from './videoPlayer';
import {aiHelperNativeUtils} from './utils';

const InnerComp = ({
    sections = [],
    bottomCard = {},
    onConsult = () => {},
    onQuestionClick = () => {},
    isH5
}) => {
    const [activeTab, setActiveTab] = useState(0);
    const containerRef = useRef(null);
    const sectionRefs = useRef([]);
    const tabsRef = useRef(null);
    const tabsScrollRef = useRef(null);
    const tabItemRefs = useRef([]);


    // 滚动TAB到可见区域
    const scrollTabIntoView = (index) => {
        const tabsScrollContainer = tabsScrollRef.current;
        const targetTab = tabItemRefs.current[index];

        if (!tabsScrollContainer || !targetTab) {
            return;
        }

        const containerRect = tabsScrollContainer.getBoundingClientRect();
        const tabRect = targetTab.getBoundingClientRect();

        // 计算tab相对于滚动容器的位置
        const tabLeft = tabRect.left - containerRect.left + tabsScrollContainer.scrollLeft;
        const tabRight = tabLeft + tabRect.width;

        const containerWidth = containerRect.width;
        const scrollLeft = tabsScrollContainer.scrollLeft;
        const scrollRight = scrollLeft + containerWidth;

        // 如果tab不完全可见，则滚动到合适位置
        if (tabLeft < scrollLeft) {
            // tab在左侧被遮挡，滚动到左边
            tabsScrollContainer.scrollTo({
                left: tabLeft - 16, // 16px 边距
                behavior: 'smooth'
            });
        }
        else if (tabRight > scrollRight) {
            // tab在右侧被遮挡，滚动到右边
            tabsScrollContainer.scrollTo({
                left: tabRight - containerWidth + 16, // 16px 边距
                behavior: 'smooth'
            });
        }
    };

    // 处理tab点击
    const handleTabClick = (index) => {
        setActiveTab(index);

        // 滚动TAB到可见区域
        scrollTabIntoView(index);

        // 滚动到对应section
        if (sectionRefs.current[index]) {
            const offsetTop = sectionRefs.current[index].offsetTop;
            // 考虑tab栏高度
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            containerRef.current.scrollTo({
                top: offsetTop - tabsHeight - 20, // 额外20px间距
                behavior: 'smooth'
            });
        }
    };
    // 处理滚动事件，更新active tab
    const handleScroll = () => {
        if (!containerRef.current || !sectionRefs.current.length) {
            return;
        }

        const scrollTop = containerRef.current.scrollTop;
        const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;

        // 计算当前应该激活的tab
        let newActiveTab = 0;
        for (let i = sectionRefs.current.length - 1; i >= 0; i--) {
            const section = sectionRefs.current[i];
            if (section && scrollTop + tabsHeight + 50 >= section.offsetTop) {
                newActiveTab = i;
                break;
            }
        }

        if (newActiveTab !== activeTab) {
            setActiveTab(newActiveTab);
        }
    };

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => container.removeEventListener('scroll', handleScroll);
        }
    }, [activeTab]);



    return (
        <div className="ai-helper-container">
            {/* 固定的tab栏 */}
            <div className="tabs-container" ref={tabsRef}>
                <div className="tabs-wrapper">
                    <div className="main-title">讲解回放</div>
                    <div className="tabs-scroll-container" ref={tabsScrollRef}>
                        <div className="tabs">
                            {sections.map((section, index) => (
                                <div
                                    key={section.id}
                                    ref={el => tabItemRefs.current[index] = el}
                                    className={classNames('tab-item', {
                                        active: activeTab === index
                                    })}
                                    onClick={() => handleTabClick(index)}
                                >
                                    {section.title}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* 滚动内容区域 */}
            <div className="content-container" ref={containerRef}>
                {sections.map((section, index) => (
                    <div
                        key={section.id}
                        className="content-section"
                        ref={el => sectionRefs.current[index] = el}
                    >
                        <div className="section-content">
                            <p className="content-text">{section.content}</p>

                            <div className="video-container">
                                <div
                                    className="video-wrapper"
                                >
                                    <VideoPlayer index={index} section={section} isH5={isH5} />
                                </div>
                            </div>

                            {/* 每个section都有自己的猜你想问 */}
                            {!isH5 && section.questions && section.questions.length > 0 && (
                                <div className="questions-section">
                                    <div className="question-prompt">
                                        猜你想问：
                                    </div>
                                    <div className="questions-list">
                                        {section.questions.map((question) => (
                                            <div
                                                key={question.id}
                                                className="question-item"
                                                onClick={() => onQuestionClick(question, section)}
                                            >
                                                {question.text}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* 底部吸底卡片 */}
            <div className="bottom-card">
                <div className="card-content">
                    <div className="card-left">
                        <img src={bottomCard.icon} alt="医生头像" className="doctor-icon" />
                        <div className="card-info">
                            <div className="card-title">{bottomCard.title}</div>
                            {bottomCard.subtitle && <div className="card-subtitle">{bottomCard.subtitle}</div>}
                        </div>
                    </div>
                    <button className="consult-button" onClick={onConsult}>
                        {bottomCard.buttonText}
                    </button>
                </div>
            </div>
        </div>
    );
};


export default function AIHelper({host = 'h5', h5Options = {}, nativeOptions = {}}) {
    const isH5 = host === 'h5';
    const sections = [
        {
            id: 1,
            title: '白癜风症状全解析',
            content: '皮肤出现不明原因的白斑？可能是白癜风在作祟！白癜风初期表现为米粒至指甲大小的白色斑片，逐渐发展为瓷白色斑块，常出现在手指、口角等暴露部位。部分患者还会伴随局部瘙痒、毛发变白等症状。',
            videoUrl: 'https://example.com/video1.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '03:48',
            questions: [
                {
                    id: 1,
                    text: '白癜风白斑和普通白斑有什么区别吗？',
                    isHighlighted: false
                },
                {
                    id: 2,
                    text: '白癜风的白斑会自己消失吗',
                    isHighlighted: true
                },
                {
                    id: 3,
                    text: '白斑发展速度快吗？多久会扩散？',
                    isHighlighted: false
                },
                {
                    id: 4,
                    text: '我想问其他问题',
                    isHighlighted: false,
                    isOther: true
                }
            ]
        },
        {
            id: 2,
            title: '白癜风8大诱因揭秘',
            content: '了解白癜风的诱发因素，有助于预防和治疗。主要包括：遗传因素、免疫系统异常、精神压力、外伤刺激、化学物质接触、内分泌失调、微量元素缺乏、感染等8大因素。这些因素可能单独作用，也可能相互影响，共同导致白癜风的发生。',
            videoUrl: 'https://example.com/video2.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '04:12',
            questions: [
                {
                    id: 1,
                    text: '遗传因素对白癜风影响大吗？',
                    isHighlighted: false
                },
                {
                    id: 2,
                    text: '精神压力真的会导致白癜风吗？',
                    isHighlighted: true
                },
                {
                    id: 3,
                    text: '外伤为什么会引发白癜风？',
                    isHighlighted: false
                },
                {
                    id: 4,
                    text: '我想问其他问题',
                    isHighlighted: false,
                    isOther: true
                }
            ]
        },
        {
            id: 3,
            title: '白癜风治疗方案',
            content: '白癜风治疗需要个性化方案，常见治疗方法包括：药物治疗（外用激素、免疫调节剂）、光疗（308激光、UVB）、手术治疗（自体表皮移植）等。早期治疗效果更佳，建议及时就医。治疗过程中需要保持耐心，配合医生制定的治疗计划。',
            videoUrl: 'https://example.com/video3.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '05:20',
            questions: [
                {
                    id: 1,
                    text: '308激光治疗效果怎么样？',
                    isHighlighted: false
                },
                {
                    id: 2,
                    text: '白癜风治疗需要多长时间？',
                    isHighlighted: true
                },
                {
                    id: 3,
                    text: '治疗期间需要注意什么？',
                    isHighlighted: false
                },
                {
                    id: 4,
                    text: '我想问其他问题',
                    isHighlighted: false,
                    isOther: true
                }
            ]
        }
    ];

    const bottomCard = {
        icon: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
        title: '领取白癜风修复白斑中药方',
        // subtitle: '1000人已预约',
        buttonText: '立即咨询'
    };

    // 咨询按钮点击处理
    const handleConsult = async () => {
        if (isH5) {
            // H5 打开任意门
        }
        else {
            const res = await aiHelperNativeUtils.getRoomInfo();
            const clueExt = res.data.clueExt || {};
            const url = clueExt.url;
            console.log(clueExt, url, '线索直播附加数据');
            aiHelperNativeUtils.openAnyDoor(url);
        }
    };

    // 问题点击处理
    const handleQuestionClick = question => {
        const isOther = question.isOther; // 猜你想问
        if (isOther) {
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
            // 调起评论
            aiHelperNativeUtils.openCommentPanel();
        }
        else {
            // 发送消息
            aiHelperNativeUtils.sendMsg(question.text);
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
        }
    };

    return (
        <div style={{height: '100vh'}}>
            <InnerComp
                sections={sections}
                bottomCard={bottomCard}
                onConsult={handleConsult}
                onQuestionClick={handleQuestionClick}
                isH5={isH5}
            />
        </div>
    );
}
